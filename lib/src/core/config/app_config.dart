import 'dart:async';
import 'dart:developer';

import 'package:wasla/src/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:restart_app/restart_app.dart';

class AppConfig extends ChangeNotifier {
  //? Loading
  bool isLoading = true;

  set loading(bool value) {
    isLoading = value;
    notifyListeners();
  }

  //? Check Internet Connection
  bool hasInternet = true;

  //? Initialize App
  Future<void> init() async {
    try {
      loading = true;

      // await AppConfig.addFcmTokenToUrl();

      await checkInternetConnection();

      if (!hasInternet) {
        loading = false;
        return;
      }

      loading = false;

      log('URL ${AppConstants.appUrl}');
    } on Exception catch (e) {
      log('Error $e');

      loading = false;
    }
  }

  static Future<void> addFcmTokenToUrl() async {
    try {
      // final token = OneSignalNotificationService.getUserId();

      // AppConstants.appUrl += '?token=$token';
    } on Exception catch (e) {
      log('Error $e');
    }
  }

  //? Check Internet Connection
  Future<void> checkInternetConnection() async {
    hasInternet = await InternetConnectionChecker.instance.hasConnection;
    notifyListeners();
  }

  InAppWebViewController? webViewController;
  PullToRefreshController? _pullToRefreshController;

  //? Initialize Pull to Refresh Controller
  PullToRefreshController get pullToRefreshController {
    _pullToRefreshController ??= PullToRefreshController(
      onRefresh: () async {
        if (webViewController != null) {
          try {
            await webViewController!.reload();
          } catch (e) {
            log('Error during refresh: $e');
          } finally {
            // Always end refreshing to dismiss the indicator
            _pullToRefreshController?.endRefreshing();
          }
        } else {
          // End refreshing even if no controller is available
          _pullToRefreshController?.endRefreshing();
        }
      },
    );
    return _pullToRefreshController!;
  }

  //? with flutter_inappwebview package
  Future<void> onWebViewCreated(InAppWebViewController controller) async {
    try {
      //? set loading
      // loading = false;

      //? set controller
      webViewController = controller;

      await addTokenToLogin(controller: controller);

      notifyListeners();
    } on TimeoutException catch (_) {
      log('Timeout occurred');

      loading = false;

      Restart.restartApp();
    } on Exception catch (e) {
      log('Error $e');

      loading = false;

      Restart.restartApp();
    }
  }

  Future<void> addTokenToLogin({
    required InAppWebViewController controller,
  }) async {
    // final currentUrl = await controller.getUrl();

    await controller.loadUrl(
      urlRequest: URLRequest(
        url: WebUri(AppConstants.appUrl),
        // url: WebUri('${currentUrl.toString()}?token=$token'),
      ),
    );
  }
}

//? Add Token To Login
// Future<void> addTokenToLogin({
//   required InAppWebViewController controller,
// }) async {
//   final currentUrl = await controller.getUrl();
//
//   final shouldAddToken = currentUrl.toString() == AppConstants.appUrl &&
//       !currentUrl.toString().contains('token=');
//
//   if (shouldAddToken) {
//     Log.w('SHOULD ADD TOKEN------');
//     final token = OneSignalNotificationService.getUserId();
//     await controller.loadUrl(
//       urlRequest: URLRequest(
//         url: WebUri('${currentUrl.toString()}?token=$token'),
//       ),
//     );
//   }
// }
// }
