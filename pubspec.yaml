name: wasla
description: "Wasla Web View App"

publish_to: 'none'

version: 1.0.4+4

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  flutter_inappwebview: ^6.1.0+1
  webview_flutter: ^4.7.0
  http: ^1.2.2
#  onesignal_flutter: ^5.1.5
  provider: ^6.1.2
  loading_animation_widget: ^1.3.0
  url_launcher: ^6.3.1
  xr_helper:
    path: packages/xr_helper
  internet_connection_checker: ^3.0.1
  restart_app: ^1.3.2
  firebase_core:

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.14.2
  flutter_native_splash: ^2.4.4

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/logo.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/logo.png"
  adaptive_icon_foreground_inset: 16

# ? dart run flutter_native_splash:create
flutter_native_splash:
  android: true
  ios: true
  web: false
  fullscreen: false
  color: '#ffffff'
  image: 'assets/images/logo.png'
  android_12:
    color: '#ffffff'
    image: 'assets/images/logo.png'


flutter:

  uses-material-design: true

  assets:
    - assets/images/